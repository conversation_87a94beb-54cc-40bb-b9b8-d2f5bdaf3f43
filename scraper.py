import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import random
import re
from typing import Dict, List, Optional, Tuple
from pandas import DataFrame
import os
from urllib.parse import urlparse
from fake_useragent import UserAgent
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from playwright.sync_api import sync_playwright
import chardet

# Configure Chinese logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('scraper.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Add Chinese translations
class ChineseFilter(logging.Filter):
    translations = {
        'DEBUG': '调试',
        'INFO': '信息',
        'WARNING': '警告',
        'ERROR': '错误',
        'CRITICAL': '严重'
    }
    
    def filter(self, record):
        if record.levelname in self.translations:
            record.levelname = self.translations[record.levelname]
        return True

logger.addFilter(ChineseFilter())

# --- Session Management ---

class BrowserSession:
    """Manages browser session with <PERSON><PERSON> for JavaScript-heavy sites like Baidu."""
    
    def __init__(self, proxy: Optional[Tuple[str, int]] = None):
        self.playwright_available = True
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        try:
            # Try to initialize Playwright
            self.playwright = sync_playwright().start()
            proxy_config = None
            if proxy:
                proxy_config = {
                    'server': f"http://{proxy[0]}:{proxy[1]}",
                    'username': proxy[2] if len(proxy) > 2 else None,
                    'password': proxy[3] if len(proxy) > 3 else None
                }
            self.browser = self.playwright.chromium.launch(
                headless=True,
                proxy=proxy_config,
                timeout=30000
            )
            self.context = self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                locale="zh-CN",
                viewport={"width": 1920, "height": 1080}
            )
            self.page = self.context.new_page()
        except Exception as e:
            self.playwright_available = False
            logger.warning("Playwright初始化失败，将使用基本请求模式: %s", str(e))
        self.context = self.browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            locale="zh-CN",
            viewport={"width": 1920, "height": 1080}
        )
        self.page = self.context.new_page()
        self.last_request_time = 0
        self.min_delay = 2.0
        self.max_delay = 5.0
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if time_since_last < delay:
            time.sleep(delay - time_since_last)
        self.last_request_time = time.time()
    
    def get(self, url: str) -> str:
        """Navigate to URL and return rendered HTML."""
        self._rate_limit()
        try:
            if not self.playwright_available:
                logger.warning("Playwright不可用，无法使用浏览器模式")
                raise Exception("Playwright不可用")
            
            self.page.goto(url, timeout=60000)  # Increased timeout for Chinese sites
            time.sleep(5)  # Wait for JavaScript rendering
            # Check for CAPTCHA
            if self.page.query_selector("#captcha"):
                logger.warning("检测到验证码，尝试解决...")
                self._handle_captcha()
            
            content = self.page.content()
            return content
        except Exception as e:
            logger.error("浏览器导航失败: %s", str(e))
            raise
        finally:
            if self.page:
                self.page.close()
    
    def _handle_captcha(self):
        """Basic CAPTCHA handling (placeholder for actual solution)."""
        logger.warning("CAPTCHA handling not implemented")
        raise Exception("CAPTCHA encountered")
    
    def close(self):
        """Close browser resources."""
        self.page.close()
        self.context.close()
        self.browser.close()
        self.playwright.stop()

class ScrapingSession:
    """Manages HTTP session with proper headers and rate limiting."""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.last_request_time = 0
        self.min_delay = 1.0
        self.max_delay = 3.0
        self._setup_session()
    
    def _setup_session(self):
        """Configure session with proper headers."""
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Make a GET request with rate limiting."""
        self._rate_limit()
        try:
            # Set default timeout if not provided
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 30
            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            logger.warning(f"Request failed for {url}: {e}")
            raise
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if time_since_last < delay:
            sleep_time = delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def rotate_user_agent(self):
        """Rotate user agent for the session."""
        self.session.headers['User-Agent'] = self.ua.random

# --- Extraction Functions ---

def extract_from_business_directory(soup: BeautifulSoup, url: str) -> Dict[str, str]:
    """Extracts contact information from Qichacha/Tianyancha pages."""
    try:
        content = ''
        
        # Define selectors for different business directories
        if 'qichacha.com' in url:
            selectors = ['.company-info', '.content', '.baseinfo', '.company-header', '.detail-content']
        elif 'tianyancha.com' in url:
            selectors = ['.company-info', '.detail', '.company-header', '.base-info', '.content-wrap']
        else:
            selectors = ['.company-info', '.content', '.detail']
        
        # Try to find content using selectors
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                content = element.get_text()
                break
        
        if not content:
            content = soup.get_text()
        
        # Enhanced patterns for Chinese business directories
        patterns = {
            'phone': [
                r'电话[：:\s]*([0-9-\s+()（）]+)',
                r'联系电话[：:\s]*([0-9-\s+()（）]+)',
                r'手机[：:\s]*([0-9-\s+()（）]+)',
                r'Tel[：:\s]*([0-9-\s+()（）]+)',
                r'座机[：:\s]*([0-9-\s+()（）]+)',
                r'固话[：:\s]*([0-9-\s+()（）]+)',
                r'客服电话[：:\s]*([0-9-\s+()（）]+)',
                r'服务热线[：:\s]*([0-9-\s+()（）]+)',
                r'咨询电话[：:\s]*([0-9-\s+()（）]+)',
                r'(\+?86\s?1\d{10}|1\d{2}[- ]?\d{4}[- ]?\d{4})',  # Enhanced mobile with +86
                r'1[3-9]\d{9}',  # Mobile numbers
                r'0\d{2,3}[-\s]?\d{7,8}',  # Landline numbers
                r'400[-\s]?\d{3}[-\s]?\d{4}',  # 400 numbers
                r'800[-\s]?\d{3}[-\s]?\d{4}',  # 800 numbers
            ],
            'email': [
                r'邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'电子邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'Email[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'联系邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'企业邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'商务邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            ],
            'address': [
                r'地址[：:\s]*([^\n]+)',
                r'注册地址[：:\s]*([^\n]+)',
                r'办公地址[：:\s]*([^\n]+)',
                r'详细地址[：:\s]*([^\n]+)',
                r'联系地址[：:\s]*([^\n]+)',
                r'公司地址[：:\s]*([^\n]+)',
                r'通讯地址[：:\s]*([^\n]+)'
            ]
        }
        
        results = {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A'}
        
        # Extract phone numbers
        phone_numbers = set()
        for pattern in patterns['phone']:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    phone = match[0] if match[0] else match[1] if len(match) > 1 else ''
                else:
                    phone = match
                # Enhanced phone number cleaning
                phone = re.sub(r'[-\s+()（）]', '', str(phone)).strip()
                # Remove country code if present
                if phone.startswith('86') and len(phone) > 11:
                    phone = phone[2:]
                elif phone.startswith('+86') and len(phone) > 13:
                    phone = phone[3:]
                
                # Validate phone number
                if len(phone) >= 7 and phone.isdigit():
                    # Additional validation for Chinese numbers
                    if (len(phone) == 11 and phone.startswith('1')) or \
                       (len(phone) >= 10 and phone.startswith('0')) or \
                       (len(phone) >= 10 and phone.startswith(('400', '800'))):
                        phone_numbers.add(phone)
        
        if phone_numbers:
            results['ScrapedPhone'] = ', '.join(sorted(phone_numbers))
        
        # Extract emails
        email_set = set()
        for pattern in patterns['email']:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    email = match[0] if match[0] else match[1] if len(match) > 1 else ''
                else:
                    email = match
                email = str(email).strip()
                if '@' in email and '.' in email and len(email) > 5:
                    # Enhanced email validation
                    skip_domains = ['example.com', 'test.com', 'placeholder', 'noreply', 'no-reply', 'dummy', 
                                   'sentry-prod', 'localhost', '127.0.0.1', 'tempmail', 'throwaway']
                    skip_patterns = ['@sentry', '@error', '@log', '@debug', '@temp']
                    
                    email_lower = email.lower()
                    if not any(skip in email_lower for skip in skip_domains) and \
                       not any(pattern in email_lower for pattern in skip_patterns) and \
                       len(email.split('@')[0]) >= 2:  # Username should be at least 2 chars
                        email_set.add(email)
        
        if email_set:
            results['ScrapedEmail'] = ', '.join(sorted(email_set))
        
        # Extract address
        for pattern in patterns['address']:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    address = match[0] if match[0] else match[1] if len(match) > 1 else ''
                else:
                    address = match
                address = str(address).strip()
                if len(address) > 10 and any(c in address for c in ['市', '区', '路', '街', '号', '县', '省', '镇']):
                    results['ScrapedAddress'] = address[:200]  # Limit length
                    break
        
        return results
        
    except Exception as e:
        logger.error(f"Error extracting from business directory: {e}")
        return {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A'}

def get_contact_info(soup: BeautifulSoup, url: str) -> Dict[str, str]:
    """Extracts contact information from any webpage using Beautiful Soup."""
    try:
        # Get both text content and HTML for comprehensive extraction
        content = soup.get_text()
        html_content = str(soup)
        
        # Enhanced phone number patterns for Chinese numbers (improved from Streamlit code)
        phone_patterns = [
            r'(\+?86\s?1\d{10}|1\d{2}[- ]?\d{4}[- ]?\d{4})',  # Chinese mobile with +86 prefix
            r'1[3-9]\d{9}',  # Mobile numbers
            r'0\d{2,3}[-\s]?\d{7,8}',  # Landline numbers
            r'\d{3,4}[-\s]?\d{7,8}',  # General landline
            r'400[-\s]?\d{3}[-\s]?\d{4}',  # 400 numbers
            r'800[-\s]?\d{3}[-\s]?\d{4}',  # 800 numbers
            r'021[-\s]?\d{8}',  # Shanghai
            r'010[-\s]?\d{8}',  # Beijing
            r'0755[-\s]?\d{8}',  # Shenzhen
            r'020[-\s]?\d{8}',  # Guangzhou
            r'\b\d{3,4}[-\s]?\d{3,4}[-\s]?\d{3,4}\b',  # General pattern
            r'\b\d{11}\b',  # 11-digit numbers
            r'\b\d{10}\b',  # 10-digit numbers
        ]
        
        phone_numbers = set()
        for pattern in phone_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                clean_phone = re.sub(r'[-\s]', '', match)
                if len(clean_phone) >= 7:  # Valid phone length
                    phone_numbers.add(clean_phone)
        
        # Enhanced email extraction
        email_patterns = [
            r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'电子邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'Email[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        ]
        
        emails = set()
        for pattern in email_patterns:
            # Search in both text and HTML content
            for text in [content, html_content]:
                matches = re.findall(pattern, text, re.IGNORECASE)
                emails.update(matches)
        
        # Filter out invalid/placeholder emails
        skip_domains = ['example.com', 'test.com', 'placeholder', 'noreply', 'no-reply', 'dummy']
        valid_emails = [
            email for email in emails 
            if not any(skip in email.lower() for skip in skip_domains) and len(email) > 5
        ]
        
        # Enhanced address extraction
        address = "N/A"
        address_keywords = [
            '地址', '联系地址', '公司地址', '办公地址', '注册地址', '详细地址',
            '通讯地址', '邮寄地址', 'Address', 'address'
        ]
        
        for keyword in address_keywords:
            patterns = [
                f'{keyword}[：:\s]*([^\n{{1,200}}]+)',
                f'{keyword}\s*[：:]\s*([^\n{{1,200}}]+)',
                f'{keyword}\s+([^\n{{1,200}}]+)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    addr_text = match.group(1).strip()
                    # Validate address has Chinese location indicators
                    if (len(addr_text) > 10 and 
                        any(c in addr_text for c in ['市', '区', '路', '街', '号', '县', '省', '镇'])):
                        address = addr_text[:200]  # Limit length
                        break
            
            if address != "N/A":
                break
        
        # Fallback: look for address patterns without keywords
        if address == "N/A":
            address_patterns = [
                r'([^\n]*?[省市区县][^\n]*?[路街道][^\n]*?[号楼][^\n]*?)',
                r'([^\n]*?\d+号[^\n]*?)',
                r'([^\n]*?[市区县][^\n]*?[路街][^\n]*?)'
            ]
            
            for pattern in address_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    candidate = matches[0].strip()
                    if len(candidate) > 15:  # Reasonable address length
                        address = candidate[:200]
                        break
        
        return {
            'ScrapedPhone': ', '.join(sorted(phone_numbers)) or 'N/A',
            'ScrapedEmail': ', '.join(sorted(valid_emails)) or 'N/A',
            'ScrapedAddress': address
        }
        
    except Exception as e:
        logger.error(f"Error extracting contact info from {url}: {e}")
        return {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A'}

# --- Search and URL Discovery ---

def search_company_urls(session: ScrapingSession, company_name: str, city: Optional[str] = None, 
                       proxy: Optional[Tuple[str, int]] = None) -> List[str]:
    """Search for company URLs using multiple search engines and strategies."""
    urls = []
    
    # Enhanced search strategies
    search_queries = [
        f'{company_name}',  # Start with simple company name
        f'{company_name} 官网',
        f'{company_name} 公司',
        f'{company_name} 联系方式',
        f'site:qichacha.com {company_name}',  # Direct business directory search
        f'site:tianyancha.com {company_name}',
    ]
    
    if city:
        search_queries.insert(1, f'{company_name} {city}')
    
    # Multiple search engines as fallback
    search_engines = [
        ('Baidu', 'https://www.baidu.com/s?wd={}'),
        ('Sogou', 'https://www.sogou.com/web?query={}'),
        ('Bing', 'https://cn.bing.com/search?q={}')
    ]
    
    for query in search_queries[:4]:  # Try more queries
        for engine_name, search_url_template in search_engines:
            try:
                search_url = search_url_template.format(query)
                logger.info(f"Searching on {engine_name}: {query}")
                
                # Use BrowserSession for Baidu, ScrapingSession for others
                if 'baidu.com' in search_url:
                    browser_session = BrowserSession(proxy)
                    try:
                        html = browser_session.get(search_url)
                        soup = BeautifulSoup(html, 'html.parser')
                    finally:
                        browser_session.close()
                else:
                    # Add specific headers for different search engines
                    headers = {}
                    if 'bing.com' in search_url:
                        headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
                    
                    response = session.get(search_url, headers=headers)
                    
                    # Check if we got blocked
                    if response.status_code == 403 or '验证码' in response.text:
                        logger.warning(f"{engine_name} blocked request, trying next engine")
                        continue
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract URLs from search results
                found_urls = extract_urls_from_search(soup, company_name)
                
                if found_urls:
                    logger.info(f"Found {len(found_urls)} URLs from {engine_name}")
                    urls.extend(found_urls)
                    
                    # If we found good results from business directories, prioritize them
                    priority_found = any('qichacha.com' in url or 'tianyancha.com' in url for url in found_urls)
                    if priority_found and len(urls) >= 3:
                        break
                
                if len(urls) >= 8:  # Have enough URLs
                    break
                    
            except Exception as e:
                logger.warning(f"Search failed on {engine_name} for query '{query}': {e}")
                continue
        
        if len(urls) >= 5:  # Have enough URLs
            break
    
    # Remove duplicates while preserving order
    unique_urls = []
    seen = set()
    for url in urls:
        if url not in seen:
            unique_urls.append(url)
            seen.add(url)
    
    logger.info(f"Total unique URLs found: {len(unique_urls)}")
    return unique_urls[:8]  # Return top 8 URLs

def generate_business_directory_urls(company_name: str) -> List[str]:
    """Generate direct URLs to business directories for the company."""
    import urllib.parse
    
    encoded_name = urllib.parse.quote(company_name)
    
    # Business directory URL patterns
    directory_urls = [
        f'https://www.qichacha.com/search?key={encoded_name}',
        f'https://www.tianyancha.com/search?key={encoded_name}',
        f'https://aiqicha.baidu.com/s?q={encoded_name}',
        f'https://www.qcc.com/search?key={encoded_name}',
        f'https://www.gsxt.gov.cn/index.html',  # National Enterprise Credit Information System
    ]
    
    logger.info(f"Generated {len(directory_urls)} fallback business directory URLs")
    return directory_urls

def extract_urls_from_search(soup: BeautifulSoup, company_name: str) -> List[str]:
    """Extract relevant URLs from search engine results."""
    urls = []
    
    # More comprehensive Baidu selectors
    selectors = [
        'h3.t a',  # Classic Baidu result title links
        '.result h3 a',
        '.c-container h3 a', 
        '.result-op h3 a',
        'h3 a',
        '.c-showurl',  # Baidu display URLs
        'a[href*="http"]'  # Any link with http
    ]
    
    skip_domains = [
        'zhihu.com', 'csdn.net', 'blog', 'weibo.com', 'baike.baidu.com',
        'wenku.baidu.com', 'tieba.baidu.com', 'news', 'wikipedia.org',
        'baidu.com', 'sogou.com', 'bing.com', 'so.com'
    ]
    
    priority_domains = ['qichacha.com', 'tianyancha.com', 'aiqicha.com', 'enterprise.com']
    
    logger.info(f"Extracting URLs for company: {company_name}")
    
    # Try different approaches to extract URLs
    all_links = []
    
    # Method 1: Use selectors
    for selector in selectors:
        links = soup.select(selector)
        logger.debug(f"Found {len(links)} links with selector '{selector}'")
        all_links.extend(links)
    
    # Method 2: Find all links in the page
    if not all_links:
        all_links = soup.find_all('a', href=True)
        logger.debug(f"Fallback: Found {len(all_links)} total links")
    
    processed_urls = set()  # Use set to avoid duplicates
    
    for link in all_links[:50]:  # Process more links but limit to avoid slowdown
        href = link.get('href')
        if not href:
            continue

        logger.debug(f"Processing link: {href[:100]}...")
        
        # Handle different URL formats
        if href.startswith('//'):
            href = 'https:' + href
        elif href.startswith('/'):
            continue  # Skip relative URLs
        elif not href.startswith('http'):
            continue  # Skip non-HTTP URLs
        
        # Handle Baidu redirect URLs more comprehensively
        if 'baidu.com/link' in href or 'baidu.com/s?' in href:
            try:
                import urllib.parse as parse_module
                parsed = parse_module.urlparse(href)
                query_params = parse_module.parse_qs(parsed.query)

                # Try different parameter names that Baidu uses
                real_url = None
                for param in ['url', 'wd', 'word']:
                    if param in query_params:
                        real_url = query_params[param][0]
                        break

                if real_url:
                    # Decode URL if it's encoded
                    try:
                        href = parse_module.unquote(real_url)
                    except:
                        href = real_url
                    logger.debug(f"Resolved Baidu redirect: {href[:100]}...")
                else:
                    continue
            except Exception as e:
                logger.debug(f"Failed to resolve Baidu redirect: {e}")
                continue
        
        # Validate URL format
        if not (href.startswith('http://') or href.startswith('https://')):
            continue
            
        # Skip unwanted domains
        if any(skip in href.lower() for skip in skip_domains):
            logger.debug(f"Skipping unwanted domain: {href[:50]}...")
            continue
        
        # Basic URL validation
        try:
            parsed = urlparse(href)
            if not parsed.netloc or len(parsed.netloc) < 3:
                continue
        except:
            continue
        
        # Add to results
        if href not in processed_urls:
            processed_urls.add(href)
            
            # Prioritize business directory sites
            if any(domain in href.lower() for domain in priority_domains):
                urls.insert(0, href)  # Add to front
                logger.info(f"Found priority domain: {href[:80]}...")
            else:
                urls.append(href)
                logger.info(f"Found URL: {href[:80]}...")
    
    logger.info(f"Extracted {len(urls)} valid URLs")
    return urls[:8]  # Return top 8 URLs

def scrape_single_company(session: ScrapingSession, company_data: Dict, 
                        proxy: Optional[Tuple[str, int]] = None) -> Dict[str, str]:
    """Scrape contact information for a single company.
    
    Args:
        session: ScrapingSession instance
        company_data: Dictionary containing company information
        proxy: Optional proxy configuration (host, port)
    """
    company_name = str(company_data['CompanyName'])
    
    # Initialize result with existing data
    result = {
        'CompanyName': company_name,
        'ExistingEmail': company_data.get('ExistingEmail', 'N/A'),
        'ExistingWebsite': company_data.get('ExistingWebsite', 'N/A'),
        'ExistingAddress': company_data.get('ExistingAddress', 'N/A'),
        'LegalRepresentative': company_data.get('LegalRepresentative', 'N/A'),
        'RegisteredCapital': company_data.get('RegisteredCapital', 'N/A'),
        'EstablishmentDate': company_data.get('EstablishmentDate', 'N/A'),
        'ScrapedPhone': 'N/A',
        'ScrapedEmail': 'N/A', 
        'ScrapedAddress': 'N/A',
        'ScrapedWebsite': 'N/A',
        'Status': 'Failed',
        'ErrorDetails': None
    }
    
    try:
        # Check if we already have sufficient data
        has_email = pd.notna(result['ExistingEmail']) and result['ExistingEmail'] not in ['-', '', 'N/A']
        has_website = pd.notna(result['ExistingWebsite']) and result['ExistingWebsite'] not in ['-', '', 'N/A']
        
        if has_email and has_website:
            result['Status'] = 'Existing data sufficient'
            logger.info(f"Skipping {company_name} - already has email and website")
            return result
        
        logger.info(f"Scraping {company_name}...")
        
        # Determine target URLs
        target_urls = []
        if has_website:
            target_urls.append(result['ExistingWebsite'])
            logger.info(f"Using existing website: {result['ExistingWebsite']}")
        else:
            # Search for company URLs - handle both Chinese and English column names
            city = company_data.get('City') or company_data.get('所属城市')
            found_urls = search_company_urls(session, company_name, city)
            target_urls.extend(found_urls)
            
            # Fallback: Try direct business directory URLs if search failed
            if not target_urls:
                logger.info("Search engines failed, trying direct business directory URLs...")
                fallback_urls = generate_business_directory_urls(company_name)
                target_urls.extend(fallback_urls)
        
        if not target_urls:
            result['Status'] = 'No valid website found'
            return result
        
        # Try each URL until we get good results
        best_result = None
        best_contact_count = 0
        
        for i, url in enumerate(target_urls[:5]):  # Try up to 5 URLs
            try:
                logger.info(f"Visiting URL {i+1}/{min(len(target_urls), 5)}: {url[:80]}...")
                
                # Add retry logic for failed requests
                max_retries = 2
                response = None
                
                for retry in range(max_retries):
                    try:
                        # Try regular HTTP request first
                        response = session.get(url, timeout=60)
                        if response.status_code == 200:
                            break
                        elif response.status_code == 403:
                            logger.warning(f"Access denied for {url}, trying browser fallback...")
                            # Fallback to browser session for blocked sites
                            try:
                                browser_session = BrowserSession(proxy)
                                html_content = browser_session.get(url)
                                browser_session.close()
                                
                                # Create a mock response object
                                class MockResponse:
                                    def __init__(self, content):
                                        self.content = content.encode('utf-8')
                                        self.status_code = 200
                                        self.encoding = 'utf-8'
                                
                                response = MockResponse(html_content)
                                logger.info(f"Browser fallback successful for {url}")
                                break
                            except Exception as browser_error:
                                logger.warning(f"Browser fallback failed: {browser_error}")
                                break
                    except Exception as e:
                        if retry == max_retries - 1:
                            raise e
                        logger.warning(f"Retry {retry + 1} for {url}: {e}")
                        time.sleep(5)  # Longer wait between retries
                
                if not response or response.status_code != 200:
                    logger.warning(f"Failed to access {url} (status: {response.status_code if response else 'None'})")
                    continue
                
                # Enhanced encoding handling for Chinese content
                try:
                    # Try to detect encoding
                    if response.encoding:
                        content = response.content.decode(response.encoding)
                    else:
                        # Fallback encoding detection for Chinese sites
                        detected = chardet.detect(response.content)
                        encoding = detected.get('encoding', 'utf-8')
                        if encoding.lower() in ['gb2312', 'gbk', 'gb18030']:
                            content = response.content.decode(encoding, errors='ignore')
                        else:
                            content = response.content.decode('utf-8', errors='ignore')
                    soup = BeautifulSoup(content, 'html.parser')
                except Exception as encoding_error:
                    logger.warning(f"Encoding detection failed, using default: {encoding_error}")
                    soup = BeautifulSoup(response.content, 'html.parser')
                result['ScrapedWebsite'] = url
                
                # Extract contact information
                is_business_directory = any(d in url.lower() for d in ['qichacha.com', 'tianyancha.com', 'aiqicha.com'])
                
                if is_business_directory:
                    logger.info("Extracting from business directory...")
                    scraped_data = extract_from_business_directory(soup, url)
                else:
                    logger.info("Extracting from general website...")
                    scraped_data = get_contact_info(soup, url)
                
                # Log what we found with more detail
                found_items = [k for k, v in scraped_data.items() if v != 'N/A']
                if found_items:
                    logger.info(f"Found contact info: {', '.join(found_items)}")
                    for k, v in scraped_data.items():
                        if v != 'N/A':
                            logger.info(f"  {k}: {v[:100]}..." if len(str(v)) > 100 else f"  {k}: {v}")
                else:
                    logger.info("No contact information found on this page")
                    # Debug: log first 500 chars of page content
                    page_text = soup.get_text()[:500].replace('\n', ' ').strip()
                    logger.debug(f"Page content preview: {page_text}...")
                
                # Update result with scraped data
                current_result = result.copy()
                current_result.update(scraped_data)
                
                # Check if we got good results
                contact_count = sum(1 for v in scraped_data.values() if v != 'N/A')
                
                if contact_count > best_contact_count:
                    best_result = current_result.copy()
                    best_contact_count = contact_count
                    logger.info(f"New best result with {contact_count} contact items")
                
                # If we got excellent results, we can stop
                if contact_count >= 2:
                    logger.info(f"Excellent results found ({contact_count} items), stopping search")
                    break
                    
            except Exception as e:
                logger.warning(f"Failed to scrape {url}: {e}")
                continue
        
        if best_result and best_contact_count > 0:
            best_result['Status'] = 'Success'
            logger.info(f"Successfully scraped {company_name} with {best_contact_count} contact items")
            return best_result
        else:
            result['Status'] = 'No contact information found'
            logger.warning(f"No contact information found for {company_name}")
            return result
            
    except Exception as e:
        logger.error(f"Scraping failed for {company_name}: {e}")
        result['Status'] = 'Scraping failed'
        result['ErrorDetails'] = str(e)
        return result

# --- Main Scraping Logic ---

def scrape_contacts(df: DataFrame, max_workers: int = 3, proxy: Optional[Tuple[str, int]] = None) -> DataFrame:
    """
    Main function to scrape contact information for companies using Beautiful Soup.
    Uses threading for improved performance while respecting rate limits.

    Args:
        df: DataFrame containing company data
        max_workers: Maximum number of concurrent workers
        proxy: Optional proxy configuration (host, port)
    """
    results = []
    
    # Convert DataFrame to list of dictionaries for easier processing
    companies = df.to_dict('records')
    
    logger.info(f"Starting to scrape {len(companies)} companies with {max_workers} workers")
    
    # Use ThreadPoolExecutor for concurrent scraping
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Create a session for each worker
        sessions = [ScrapingSession() for _ in range(max_workers)]
        session_index = 0
        
        # Submit tasks
        future_to_company = {}
        for company in companies:
            session = sessions[session_index % len(sessions)]
            future = executor.submit(scrape_single_company, session, company, proxy)
            future_to_company[future] = company['CompanyName']
            session_index += 1
        
        # Collect results as they complete
        for future in as_completed(future_to_company):
            company_name = future_to_company[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed scraping for {company_name} - Status: {result['Status']}")
            except Exception as e:
                logger.error(f"Exception occurred for {company_name}: {e}")
                # Create a failed result
                failed_result = {
                    'CompanyName': company_name,
                    'Status': 'Exception occurred',
                    'ErrorDetails': str(e)
                }
                results.append(failed_result)
    
    logger.info(f"Scraping completed. Processed {len(results)} companies")
    return pd.DataFrame(results)

# --- Main Execution ---

def main():
    """
    Main execution block to run the scraper.
    
    Proxy can be configured via environment variables:
    - SCRAPER_PROXY_HOST
    - SCRAPER_PROXY_PORT
    """
    input_file = 'data/查询企业名单20250819 - 副本.xlsx'
    output_file = 'data/scraped_contacts.xlsx'

    if not os.path.exists(input_file):
        logger.error(f"Input file '{input_file}' not found.")
        return

    # Load and validate data
    try:
        df = pd.read_excel(input_file)
        logger.info(f"Loaded {len(df)} companies from {input_file}")
    except Exception as e:
        logger.error(f"Failed to read {input_file}: {e}")
        return
    
    # Get company name column from environment or use default
    company_col = os.getenv('COMPANY_NAME_COL', 'CompanyName')
    
    # Check for Chinese column names first
    chinese_company_col = '企业名称'
    if chinese_company_col in df.columns:
        company_col = chinese_company_col
        logger.info(f"Using Chinese company name column: {company_col}")
    
    # Ensure required column exists
    if company_col not in df.columns:
        logger.error(f"Required column '{company_col}' not found in the input file.")
        logger.info(f"Available columns: {', '.join(df.columns)}")
        return
    
    # Rename column for internal use and map other Chinese columns
    column_mapping = {
        company_col: 'CompanyName',
        '邮箱': 'ExistingEmail',
        '网址': 'ExistingWebsite', 
        '企业地址': 'ExistingAddress',
        '法定代表人': 'LegalRepresentative',
        '注册资本': 'RegisteredCapital',
        '成立日期': 'EstablishmentDate',
        '所属城市': 'City'
    }
    
    # Only rename columns that exist in the dataframe
    existing_mappings = {k: v for k, v in column_mapping.items() if k in df.columns}
    df = df.rename(columns=existing_mappings)
    
    logger.info(f"Mapped {len(existing_mappings)} columns: {list(existing_mappings.keys())}")
    
    # Start scraping
    try:
        scraped_df = scrape_contacts(df, max_workers=2)  # Conservative threading
        
        # Save results
        scraped_df.to_excel(output_file, index=False)
        logger.info(f"Scraping complete. Results saved to {output_file}")
        
        # Print summary
        success_count = len(scraped_df[scraped_df['Status'] == 'Success'])
        total_count = len(scraped_df)
        logger.info(f"Success rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
    except Exception as e:
        logger.error(f"Scraping failed: {e}")

if __name__ == "__main__":
    main()